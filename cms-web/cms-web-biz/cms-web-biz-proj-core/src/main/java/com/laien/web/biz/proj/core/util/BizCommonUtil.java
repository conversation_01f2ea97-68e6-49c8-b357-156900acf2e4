package com.laien.web.biz.proj.core.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.laien.common.core.enums.biz.CommonVideoDirectionEnums;
import com.laien.web.frame.exception.BizException;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * 业务相关通用工具类
 *
 * <AUTHOR>
 * @since 2025/09/08
 */
public class BizCommonUtil {


    /**
     * 校验Workout中左右动作是否成对且顺序正确,默认抛出异常
     * @param videoInfos
     */
    public static void checkLeftRightVideos(List<WorkoutVideoInfo> videoInfos) {
        checkLeftRightVideos(videoInfos, true);
    }

    /**
     * 校验Workout中左右动作是否成对且顺序正确,不抛出异常
     * @param videoInfos
     * @return 错误信息列表
     */
    public static List<String> checkLeftRightVideosNoThrow(List<WorkoutVideoInfo> videoInfos) {
        return checkLeftRightVideos(videoInfos, false);
    }

    /**
     * 校验Workout中左右动作是否成对且顺序正确
     *
     * 校验规则：
     * 1. 必须保证一个workout中左右动作成对出现
     * 2. 同一个动作的左右必须先左后右
     * 3. 不限制左右动作必须连续出现（左右动作中间也能加动作，不管是单个或左右动作）
     *
     * @param videoInfos 视频信息列表
     * @param needThrowError 是否需要抛出异常
     * @return 校验错误信息列表
     * <AUTHOR>
     * @since 2025/09/08
     */
    public static List<String> checkLeftRightVideos(List<WorkoutVideoInfo> videoInfos, boolean needThrowError) {
        if (CollUtil.isEmpty(videoInfos)) {
            return ListUtil.empty();
        }

        List<String> errors = new ArrayList<>();

        // 将所有右video的id和值转为map
        LinkedHashMap <Integer, List<WorkoutVideoInfo>> rightVideoMap = new LinkedHashMap<>();
        for (int i = 0, videoInfosSize = videoInfos.size(); i < videoInfosSize; i++) {
            WorkoutVideoInfo video = videoInfos.get(i);
            if (video.getDirection() == CommonVideoDirectionEnums.RIGHT) {
                video.setVideoIndex(i);
                rightVideoMap.computeIfAbsent(video.getVideoId(), k -> new ArrayList<>()).add(video);
            }
        }

        // 遍历每个视频，找到左动作并进行配对
        for (int i = 0; i < videoInfos.size(); i++) {
            WorkoutVideoInfo video = videoInfos.get(i);

            // 只处理左动作
            if (video.getDirection() != CommonVideoDirectionEnums.LEFT) {
                continue;
            }

            Integer leftVideoId = video.getVideoId();
            Integer rightVideoId = video.getRightVideoId();

            List<WorkoutVideoInfo> rightVideos = rightVideoMap.get(rightVideoId);
            if (CollUtil.isEmpty(rightVideos)) {
                errors.add("Left video " + leftVideoId + " at index " + i + " has no matching right video " + rightVideoId);
                continue;
            }

            // 获取第一个匹配的右动作并检查位置
            WorkoutVideoInfo matchedRightVideo = rightVideos.get(0);
            if (i >= matchedRightVideo.getVideoIndex()) {
                errors.add("Left video " + leftVideoId + " at index " + i + " must appear before right video " + rightVideoId + " at index " + matchedRightVideo.getVideoIndex());
            }

            // 剔除已匹配的右动作，防止重复使用
            rightVideos.remove(0);
        }

        // 检查剩余的右动作（未被匹配的）
        for (List<WorkoutVideoInfo> rightVideos : rightVideoMap.values()) {
            for (WorkoutVideoInfo rightVideo : rightVideos) {
                errors.add("Right video " + rightVideo.getVideoId() + " at index " + rightVideo.getVideoIndex() + " has no matching left video");
            }
        }

        if (!errors.isEmpty() && needThrowError) {
            throwErrorLines(errors);
        }
        return errors;
    }

    private static void throwErrorLines(List<String> errors) {
        StringBuilder errorMessage = new StringBuilder("Video left-right validation failed: ");
        for (int i = 0; i < errors.size(); i++) {
            if (i > 0) {
                //增加分号和换行
                errorMessage.append("; \n ");
            }
            errorMessage.append(errors.get(i));
        }
        throw new BizException(errorMessage.toString());
    }


    /**
     * Workout视频信息
     * 用于左右动作校验的通用对象
     */
    @Data
    public static class WorkoutVideoInfo {
        private Integer videoId;
        private Integer rightVideoId;
        private CommonVideoDirectionEnums direction;
        private int videoIndex; // 不在构造方法里

        public WorkoutVideoInfo(Integer videoId, Integer rightVideoId, CommonVideoDirectionEnums direction) {
            this.videoId = videoId;
            this.rightVideoId = rightVideoId;
            this.direction = direction;
        }
    }
}
