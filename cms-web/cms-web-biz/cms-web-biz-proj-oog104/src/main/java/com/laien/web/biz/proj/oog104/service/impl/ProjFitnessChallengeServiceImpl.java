package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.service.impl.ProjLmsI18nServiceImpl;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessChallenge;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessChallengeMapper;
import com.laien.web.biz.proj.oog104.mapstruct.ProjFitnessChallengeMapStruct;
import com.laien.web.biz.proj.oog104.request.ProjFitnessChallengeAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessChallengePageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessChallengeUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessChallengeDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessChallengePageVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessManualWorkoutPageVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessChallengeManualWorkoutRelationService;
import com.laien.web.biz.proj.oog104.service.IProjFitnessChallengeService;
import com.laien.web.biz.proj.oog104.service.IProjFitnessManualWorkoutService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.request.IdReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.BizExceptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * proj_fitness_challenge Service实现
 * <p>
 *
 * <AUTHOR>
 * @since 2025/08/15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjFitnessChallengeServiceImpl
        extends ServiceImpl<ProjFitnessChallengeMapper, ProjFitnessChallenge>
        implements IProjFitnessChallengeService {

    private final IProjFitnessChallengeManualWorkoutRelationService relationService;
    private final IProjFitnessManualWorkoutService manualWorkoutService;
    private final ProjFitnessChallengeMapStruct mapStruct;
    private final ProjLmsI18nServiceImpl projLmsI18nService;

    @Override
    public PageRes<ProjFitnessChallengePageVO> page(ProjFitnessChallengePageReq pageReq, Integer projId) {
        LambdaQueryWrapper<ProjFitnessChallenge> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessChallenge::getProjId, projId)
                .eq(ProjFitnessChallenge::getDelFlag, GlobalConstant.NO);

        queryWrapper.like(StrUtil.isNotBlank(pageReq.getName()), ProjFitnessChallenge::getName, pageReq.getName())
                .eq(pageReq.getType() != null, ProjFitnessChallenge::getType, pageReq.getType())
                .eq(pageReq.getStatus() != null, ProjFitnessChallenge::getStatus, pageReq.getStatus())
                .eq(pageReq.getSubscription() != null, ProjFitnessChallenge::getSubscription, pageReq.getSubscription())
                .eq(pageReq.getId() != null, ProjFitnessChallenge::getId, pageReq.getId());


        queryWrapper.orderByDesc(ProjFitnessChallenge::getId);

        IPage<ProjFitnessChallenge> page = this.page(new Page<>(pageReq.getPageNum(), pageReq.getPageSize()), queryWrapper);
        List<ProjFitnessChallenge> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new PageRes<>(pageReq.getPageNum(), pageReq.getPageSize(), 0L, 0L, Collections.emptyList());
        }

        List<ProjFitnessChallengePageVO> voList = mapStruct.toListVOList(records);

        // 设置workout数量
        if (CollUtil.isNotEmpty(voList)) {
            List<Integer> challengeIds = voList.stream().map(ProjFitnessChallengePageVO::getId).collect(Collectors.toList());
            Map<Integer, List<Integer>> workoutMap = relationService.mapChallengeWorkouts(challengeIds);
            voList.forEach(vo -> {
                List<Integer> workoutIds = workoutMap.get(vo.getId());
                vo.setWorkoutCount(CollUtil.isNotEmpty(workoutIds) ? workoutIds.size() : GlobalConstant.ZERO);
            });
        }

        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), voList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(ProjFitnessChallengeAddReq addReq, Integer projId) {
        // 校验名称重复
        checkNameDuplicate(addReq.getName(), null, projId);

        ProjFitnessChallenge entity = mapStruct.toEntity(addReq);
        entity.setProjId(projId);

        // 先保存获取ID
        save(entity);

        // 生成Event Name并更新
        if (StrUtil.isBlank(entity.getEventName())) {
            String eventName = generateEventName(entity.getName(), entity.getId());
            entity.setEventName(eventName);
            updateById(entity);
        }

        // 保存关联关系
        if (CollUtil.isNotEmpty(addReq.getWorkoutList())) {
            relationService.saveChallengeWorkoutRelations(entity.getId(),
                    addReq.getWorkoutList().stream().map(IdReq::getId).collect(Collectors.toList()), projId);
        }
        // 触发翻译
        projLmsI18nService.handleI18n(Collections.singletonList(entity), projId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjFitnessChallengeUpdateReq updateReq, Integer projId) {
        // 校验名称重复
        checkNameDuplicate(updateReq.getName(), updateReq.getId(), projId);

        ProjFitnessChallenge entity = mapStruct.toEntity(updateReq);
        entity.setProjId(projId);

        updateById(entity);

        // 更新关联关系
        relationService.updateChallengeWorkoutRelations(updateReq.getId(),
                updateReq.getWorkoutList().stream().map(IdReq::getId).collect(Collectors.toList()), projId);
        // 触发翻译
        projLmsI18nService.handleI18n(Collections.singletonList(entity), projId);
    }

    @Override
    public ProjFitnessChallengeDetailVO findDetailById(Integer id) {
        ProjFitnessChallenge entity = getById(id);
        if (entity == null) {
            return null;
        }

        ProjFitnessChallengeDetailVO detailVO = mapStruct.toDetailVO(entity);

        // 设置关联的workout列表
        List<Integer> workoutIds = relationService.getWorkoutIdsByChallengeId(id);
        if (CollUtil.isNotEmpty(workoutIds)) {
            Map<Integer, ProjFitnessManualWorkoutPageVO> workoutMap = manualWorkoutService.listByIds(workoutIds).stream()
                    .collect(Collectors.toMap(ProjFitnessManualWorkoutPageVO::getId, w -> w));
            //按workoutIds排序
            List<ProjFitnessManualWorkoutPageVO> sortedWorkoutList = workoutIds.stream()
                    .map(workoutMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            detailVO.setWorkoutList(sortedWorkoutList);
        } else {
            detailVO.setWorkoutList(Collections.emptyList());
        }

        return detailVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEnableByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }

        LambdaUpdateWrapper<ProjFitnessChallenge> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjFitnessChallenge::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjFitnessChallenge::getStatus, GlobalConstant.STATUS_DISABLE, GlobalConstant.STATUS_DRAFT)
                .in(CollUtil.isNotEmpty(idList), ProjFitnessChallenge::getId, idList);

        update(new ProjFitnessChallenge(),updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDisableByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }

        LambdaUpdateWrapper<ProjFitnessChallenge> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjFitnessChallenge::getStatus, GlobalConstant.STATUS_DISABLE)
                .eq(ProjFitnessChallenge::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(CollUtil.isNotEmpty(idList), ProjFitnessChallenge::getId, idList);

        update(new ProjFitnessChallenge(),updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }

        LambdaUpdateWrapper<ProjFitnessChallenge> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjFitnessChallenge::getDelFlag, GlobalConstant.YES)
                .eq(ProjFitnessChallenge::getStatus, GlobalConstant.NO)
                .in(CollUtil.isNotEmpty(idList), ProjFitnessChallenge::getId, idList);
        boolean success = update(new ProjFitnessChallenge(),updateWrapper);
        if (success) {
            // 删除关联关系
            relationService.deleteByChallengeIds(idList);
        }
    }



    /**
     * 生成Event Name
     */
    private String generateEventName(String name, Integer id) {
        if (StrUtil.isBlank(name)) {
            name = "challenge";
        }

        // 使用name + id的组合生成eventName
        String eventName = name + "_" + id;

        if (eventName.length() > 127) {
            eventName = eventName.substring(0, 127);
        }

        return eventName;
    }

    @Override
    public void checkNameDuplicate(String name, Integer id, Integer projId) {
        if (StrUtil.isBlank(name)) {
            return;
        }

        LambdaQueryWrapper<ProjFitnessChallenge> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessChallenge::getName, name)
                .ne(id != null, ProjFitnessChallenge::getId, id)
                .eq(ProjFitnessChallenge::getProjId, projId)
                .eq(ProjFitnessChallenge::getDelFlag, GlobalConstant.NO);

        BizExceptionUtil.throwIf(count(queryWrapper) > GlobalConstant.ZERO,
                "Challenge name already exists: {}", name);
    }
}
